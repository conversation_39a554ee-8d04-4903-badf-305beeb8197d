from unittest.mock import patch


class TestJurisdictionUtils:

    @patch('app.utils.jurisdiction_utils.FERMI_CODE_TO_NATLANG_MAP')
    def test_get_jurisdictions_from_request_with_valid_jurisdictions(self, mock_fermi_map):
        """Test get_jurisdictions_from_request with valid jurisdiction codes in additional_user_inputs"""
        # Arrange
        valid_codes = {'US', 'CA', 'UK', 'ALLCASES'}
        mock_fermi_map.__contains__ = lambda self, key: key in valid_codes

        from app.utils.jurisdiction_utils import get_jurisdictions_from_request

        additional_user_inputs = {
            "jurisdictions": ["US", "CA", "UK"]
        }

        # Act
        result = get_jurisdictions_from_request(additional_user_inputs)

        # Assert
        assert result == ["US", "CA", "UK"]

    @patch('app.utils.jurisdiction_utils.FERMI_CODE_TO_NATLANG_MAP')
    def test_get_jurisdictions_from_request_with_empty_input_returns_default(self, mock_fermi_map):
        """Test get_jurisdictions_from_request returns default ALLCASES when no jurisdictions provided"""
        # Arrange
        from app.utils.jurisdiction_utils import get_jurisdictions_from_request

        additional_user_inputs = {}

        # Act
        result = get_jurisdictions_from_request(additional_user_inputs)

        # Assert
        assert result == ["ALLCASES"]
