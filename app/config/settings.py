import json
import os
from functools import lru_cache
from typing import Optional

from configuration_utils.configuration_core import CoreSettings, replace_region_values_in_settings
from configuration_utils.constants import Constants as SessionConstants
from conversation_core.celery_config.base_config import CeleryBaseSettings
from conversation_core.shared.constants import Constants
# from conversation_core.shared.models.answer_profile import AnswerProfile
from raslogger.logging_factory import LoggingFactory

from services.helm_service import helm_service

logger = LoggingFactory.get_logger(__name__)


class Settings(CoreSettings):
    LOGGING_LEVEL: str = "INFO"
    PROJECT_NAME: str = "Research Application Services (RAS) AI Agent Claims Explorer"
    LOGGER_FIX_LIST: str
    AWS_SES_SECRET: str
    DISABLE_ALL_PY_WARNINGS: Optional[bool] = True
    patch_unittest: bool = False

    DEFAULT_PROFILE_MAPPING_SECRET: str
    DYNAMO_TABLE_NAME: str
    S3_BUCKET: str
    GCS_USER_SECRET: str
    GCS_URL: str
    TEMP_FOLDER: str

    LLM_PROXY_BASE_URL: str

    REDIS_METRICS_CLUSTER_RAS1: Optional[str] = None
    REDIS_METRICS_CLUSTER: Optional[str] = None
    CACHE_STRATEGY: Optional[str] = "lru_timed"
    RAS_TOOLS_URL: str
    RAS_RUN_TOOLS_V1_API_RESOURCE_PATH: str
    RAS_RUN_TOOLS_V2_API_RESOURCE_PATH: str
    RAS_LIST_TOOLS_API_RESOURCE_PATH: str
    # Cobalt URL, used by Labs' DR Wheel for Entity Resolution in their PostProcessor method for resolving citations to GUIDs
    COBALT_URL: str
    COBALT_ENTITY_ID_RESOURCE_PATH: str
    AI_CONVERSATIONS_URL: str
    WORKER_CONCURRENCY: Optional[int] = 1
    MONITORING_ENABLED: Optional[bool] = True

    # AGENT SETTINGS
    AGENT_DEPLOYMENT_TYPE: str = "worker"

    DOC_SCORING_SAGEMAKER_ENDPOINT_NAME: str
    SAGEMAKER_EMBEDDING_ENDPOINT: str

    OPEN_SEARCH_ENDPOINT: str
    OPEN_SEARCH_SECRET_ID: str

    # BLACK_LETTER_LAW_BASE_URL: str

    CLUSTER_SHORT: str


def generate_default_headers(chat_profile: str,
                             auth_token: str,
                             conversation_id: str,
                             conversation_entry_id: str,
                             conversation_type: str,
                             # answer_profile: AnswerProfile,
                             user_session: Optional[dict] = None,
                             metadata: Optional[dict] = None
                             ) -> dict:
    default_headers = {
        Constants.LLM_PROXY_API_KEY: "__none__",
        Constants.LLM_CHAT_PROFILE_NAME_HEADER: chat_profile,
        Constants.LLM_PROXY_AUTHORIZATION_HEADER: auth_token,
        Constants.LLM_CHILD_ENTRY_HEADER: f"conversation_entry_id={conversation_entry_id}",
        Constants.LLM_PARENT_ENTRY_HEADER: f"conversation_id={conversation_id}",
        Constants.LLM_TYPE_HEADER: conversation_type,
        Constants.USER_CLASSIFICATION_HEADER: user_session.get(SessionConstants.SESSION_USER_CLASSIFICATION, "unknown"),
        Constants.USER_SENSITIVITY_HEADER: user_session.get(SessionConstants.SESSION_USER_SENSITIVITY, "STANDARD"),
        Constants.PRODUCT_VIEW: user_session.get(SessionConstants.SESSION_PRODUCT_VIEW),
        Constants.PRODUCT_NAME: user_session.get(SessionConstants.SESSION_PRODUCT_NAME),
        Constants.BLUE_GREEN_ROUTING_HEADER: "green" if metadata.get(Constants.TASK_MD_ROUTE_TO_GREEN, False) else "blue",
    }

    # Only add GENERIC_USER_ID_HEADER and GENERIC_SESSION_ID_HEADER if values exist
    if user_session:
        user_guid = user_session.get(SessionConstants.SESSION_USER_GUID)
        session_id = user_session.get(SessionConstants.SESSION_SESSION_ID)
        if user_guid:
            default_headers[Constants.GENERIC_USER_ID_HEADER] = user_guid
        if session_id:
            default_headers[Constants.GENERIC_SESSION_ID_HEADER] = session_id

    if helm_service.is_green:
        default_headers[Constants.BLUE_GREEN_ROUTING_HEADER] = "green"

    return default_headers


@lru_cache()
def get_settings():
    logger.info("current working directory: " + os.getcwd())
    logger.info("getting settings from file: " + ".env." + os.getenv("ENVIRONMENT", "local"))
    settings = Settings()
    updated_settings = replace_region_values_in_settings(settings)
    logger.info("settings are: " + json.dumps(updated_settings.dict()))
    return updated_settings


@lru_cache()
def get_celery_settings():
    settings = CeleryBaseSettings()
    return settings
