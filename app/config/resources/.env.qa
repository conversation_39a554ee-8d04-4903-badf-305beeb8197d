ENVIRONMENT=qa
GCS_URL=https://entitlement-qa.gcs.int.thomsonreuters.com/v1/token
GCS_USER_SECRET=a207891/ras-search/ai-acceleration/qa/gcs-client-worker-secret
DEFAULT_PROFILE_MAPPING_SECRET=a207891/ras-search/ai-acceleration/qa/profile-override-mappings
DYNAMO_TABLE_NAME=a207891-ai-acceleration-conversations-qa
S3_BUCKET=a207891-ai-acceleration-conversations-qa-{region_short}
RAS_CONFIG_BASE_URL=http://ras-configuration-py-infra-service-qa.207891-ras-search-ai-qa.svc.cluster.local
TEMP_FOLDER=/temp
LOGGER_FIX_LIST="gunicorn,uvicorn,langchain.chat_models.openai,ddtrace.profiling,celery,flower,kombu,redis,asyncio,boto,kms,amazon,azure"
EVENT_RECEIVER_ENDPOINT=http://event-receiver-infra-service-qa.208203-events-qa.svc.cluster.local/events/v2/event
EVENTS_ENABLED=True
DATADOG_HOST=https://app.ddog-gov.com
DATADOG_API_SECRET_NAME=a207891/ras-search/ai-acceleration/qa/ddog-gov-api-secret
DATADOG_METRIC_PREFIX=ras.search.conversations
DATADOG_METRICS_ENABLED=True
AWS_SES_SECRET=a207891/ras-search/ai-acceleration/qa/ses-credentials
LLM_PROXY_BASE_URL=http://llm-proxy-infra-service-qa.207891-ras-search-ai-qa.svc.cluster.local/api/v3/proxy
LOGGING_LEVEL=INFO
RAS_TOOLS_URL=https://tools-apis-qa.plexus-{cluster_short}-pp{region_short}.5771.aws-int.thomsonreuters.com
RAS_RUN_TOOLS_V1_API_RESOURCE_PATH=api/v1/ras-tools
RAS_RUN_TOOLS_V2_API_RESOURCE_PATH=api/v2/ras-tools
RAS_LIST_TOOLS_API_RESOURCE_PATH=api/v2/tools
COBALT_URL=http://ai-acceleration-infra-service-qa.207891-ras-search-ai-qa.svc.cluster.local
COBALT_ENTITY_ID_RESOURCE_PATH=ai/v3/cobalt/entityidsearch
REDIS_METRICS_CLUSTER_RAS1=rediss://master.a207891-worker-metrics-qa-{region_short}-enc.gyywqc.{region_short}.cache.amazonaws.com:6379?ssl_cert_reqs=CERT_REQUIRED
REDIS_METRICS_CLUSTER_RAS2=rediss://master.a207891-worker-metrics2-qa-{region_short}-enc.gyywqc.{region_short}.cache.amazonaws.com:6379?ssl_cert_reqs=CERT_REQUIRED
AI_CONVERSATIONS_URL=http://ai-conversations-infra-service-qa.207891-ras-search-ai-qa.svc.cluster.local
CACHE_STRATEGY=redis
WORKER_CONCURRENCY=15
CLUSTER_SHORT={cluster_short}
SAGEMAKER_EMBEDDING_ENDPOINT=a207891-ai-acceleration-coco-fips-qa-{region_short}
DOC_SCORING_SAGEMAKER_ENDPOINT_NAME=a207891-ai-acceleration-wlqa-headnotes-scorer-qa-{region_short}
OPEN_SEARCH_ENDPOINT=https://vpc-a207891-ai-accel-qa-{region_short}-mkdhoyw3prstcsctoo6nwvgmcq.{region_long}.es.amazonaws.com
OPEN_SEARCH_SECRET_ID=a207891-ai-accel-qa-{region_short}-masterUserPassphrase
BLACK_LETTER_LAW_BASE_URL=http://ai-rag-westlaw-bll-infra-service-qa.207891-ras-search-ai-qa.svc.cluster.local