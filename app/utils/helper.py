"""
Helper utilities for the claims explorer service.
This module provides the missing functions expected by the claims_explorer_service package.
"""

from typing import List, Dict, Any
from jurisdiction_utils.static_values.fermi_code_to_natlang import FERMI_CODE_TO_NATLANG_MAP


def create_jurisdiction_info_from_fermi_codes(fermi_codes: List[str]) -> List[Dict[str, Any]]:
    """
    Create jurisdiction information objects from Fermi codes.
    Args:
        fermi_codes: List of Fermi jurisdiction codes (e.g., ['US', 'CA', 'UK'])
    Returns:
        List of jurisdiction info dictionaries with code and name
    """
    jurisdiction_info = []
    for code in fermi_codes:
        if code in FERMI_CODE_TO_NATLANG_MAP:
            jurisdiction_info.append({
                'code': code,
                'name': FERMI_CODE_TO_NATLANG_MAP[code],
                'fermi_code': code
            })
        else:
            # Fallback for unknown codes
            jurisdiction_info.append({
                'code': code,
                'name': code,  # Use code as name if mapping not found
                'fermi_code': code
            })
    return jurisdiction_info
