from typing import List
from jurisdiction_utils.static_values.fermi_code_to_natlang import FERMI_CODE_TO_NATLANG_MAP


def get_jurisdictions_from_request(additional_user_inputs: dict) -> List[str]:
    """Extract jurisdictions from additional user inputs."""
    # We want to get the jurisdictions from additional_user_inputs if possible, use the jurisdictions parameter from generate_answer()
    # as a fallback, and if both of them are empty then default to ALLCASES.
    preferred_jurisdictions: list[str] = additional_user_inputs.get("jurisdictions", [])
    if preferred_jurisdictions:
        jurisdictions = preferred_jurisdictions
    else:
        jurisdictions = []

    # Ensure that no empty Strings get passed to the Agents
    jurisdictions = [code for code in jurisdictions if code.strip()]

    if jurisdictions:
        if any(code not in FERMI_CODE_TO_NATLANG_MAP for code in jurisdictions):
            raise ValueError(f"One or more of the Fermi jurisdiction codes provided in the request are not valid: {jurisdictions}")
        else:
            return jurisdictions
    else:
        # Default jurisdiction
        return ["ALLCASES"]
