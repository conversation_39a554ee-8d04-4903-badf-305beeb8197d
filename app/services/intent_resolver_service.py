# WARNING: This is deprecated functionality using the V2 conversation endpoint and V3 task/service implementations!
# Use the V3 conversation endpoint and use V4 implementations instead.

# from typing import Optional, List, Any
#
# from conversation_core.shared.dynamo_helper import ConversationDB
# from conversation_core.shared.dynamo_helper_v2 import ConversationDB as ConversationDBV2
# from conversation_core.shared.enums import ConversationActionType
# from conversation_core.shared.models.answer_profile import AnswerProfile
# from conversation_core.shared.services.intent_resolver_base import IntentResolverServiceBase
# from conversation_core.shared.worker.worker_task import WorkerTask
# from raslogger import LoggingFactory
#
# from config.settings import get_settings
# from services.answer_profile_service import answer_profile_service
# from worker.conversation_tasks import ConversationTaskV2
#
# logger = LoggingFactory.get_logger(__name__)
# settings = get_settings()
# dynamo_db = ConversationDB(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)
# dynamo_db_v2 = ConversationDBV2(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)
# conversation_v2 = ConversationTaskV2()
#
#
# class IntentResolverService(IntentResolverServiceBase):
#     def __init__(self):
#         super().__init__(
#             settings=settings,
#             dynamo_db=dynamo_db,
#             dynamo_db_v2=dynamo_db_v2,
#             answer_profile_service=answer_profile_service,
#         )
#
#     async def check_intent(
#         self,
#         is_new_conversation: bool,
#         user_id: str,
#         user_input: str,
#         answer_solution_profile: str,
#         jurisdictions_override: Optional[List[str]],
#         content_types_override: Optional[List[str]],
#         conversation_id: str,
#         conversation_entry_id: str,
#         conversation_action_type: ConversationActionType,
#         auth_token: str,
#         worker_task: WorkerTask,
#         answer_profile: AnswerProfile,
#         tags: Optional[List[str]] = None,
#         user_session: dict = None,
#         meta_data: dict = None,
#         conversation_history: List[dict] = None,
#     ) -> Any:
#         pass
#
#     def auto_submit_intent(
#         self,
#         is_new_conversation: bool,
#         user_id: str,
#         user_input: str,
#         answer_solution_profile: str,
#         jurisdictions_override: Optional[List[str]],
#         content_types_override: Optional[List[str]],
#         conversation_id: str,
#         conversation_entry_id: str,
#         conversation_action_type: ConversationActionType,
#         auth_token: str,
#         worker_task: WorkerTask,
#         answer_profile: AnswerProfile,
#         user_session: dict = None,
#         meta_data: dict = None,
#     ):
#         pass
#
#     def handle_error(
#         self,
#         user_id: str,
#         conversation_id: str,
#         conversation_entry_id: str,
#         answer_profile: AnswerProfile,
#         meta_data: dict,
#         ex: Exception,
#     ):
#         pass
