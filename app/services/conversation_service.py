from asyncio import Future, CancelledError
from typing import Optional, List

from claims_explorer_service.services.claims_explorer_service import ClaimsExplorerService
from gcs_utils.entitlement_client import EntitlementClient
from conversation_core.shared.enums import ConversationActionType
from conversation_core.shared.enums import RetrieveConversationEntryStatuses
from conversation_core.shared.models.answer_profile import AnswerProfile, ActionSequence
from conversation_core.shared.models.errors import ErrorModel
from conversation_core.shared.services.v4.conversation_service_base import ConversationServiceBaseV4
from conversation_core.shared.worker.worker_task import WorkerTask
from raslogger import LoggingFactory

from config.settings import get_settings
from services import common_services
from services.answer_profile_service import answer_profile_service
from services.email_service import EmailService
from utils.exception_mappings import get_error_code, get_error_message, get_error_retryable
from utils.jurisdiction_utils import get_jurisdictions_from_request

logger = LoggingFactory.get_logger(__name__)
settings = get_settings()
email_service = EmailService(ses_secret=settings.AWS_SES_SECRET)
dynamo_db_v2 = common_services.dynamo_db_v2


class ConversationService(ConversationServiceBaseV4):
    def __init__(self, entitlement_client: EntitlementClient):
        super().__init__(settings=settings,
                         dynamo_db_v2=dynamo_db_v2,
                         email_service=email_service,
                         answer_profile_service=answer_profile_service,
                         entitlement_client=entitlement_client)

    def get_error_details(self, ex: Exception) -> ErrorModel:
        error_model = ErrorModel()
        error_model.is_retryable = get_error_retryable(ex)
        error_model.code = get_error_code(ex)
        error_model.message = get_error_message(ex)
        return error_model

    def handle_error(
            self, user_id: str, conversation_id: str, conversation_entry_id: str, answer_profile: AnswerProfile,
            ex: Exception
    ):
        pass

    def finish_conversation(
            self,
            user_id: str,
            user_input: str,
            conversation_id: str,
            conversation_entry_id: str,
            answer_solution_profile: str,
            overrides: Optional[dict],
            filters: Optional[dict],
            conversation_action_type: ConversationActionType,
            auth_token: str,
            worker_task: WorkerTask,
            additional_user_inputs: Optional[dict] = None,
            tags: Optional[List[str]] = None,
            user_session: dict = None,
            meta_data: dict = None,
            results: any = None,
    ):
        pass

    async def generate_answer(
            self,
            user_id: str,
            user_input: str,
            conversation_id: str,
            conversation_entry_id: str,
            answer_solution_profile: str,
            answer_profile: AnswerProfile,
            jurisdictions: Optional[List[str]],
            content_types: Optional[List[str]],
            content_types_exclude: Optional[List[str]],
            conversation_action_type: ConversationActionType,
            auth_token: str,
            worker_task: WorkerTask,
            task_action_sequence: ActionSequence,
            additional_user_inputs: Optional[dict] = None,
            tags: Optional[List[str]] = None,
            user_session: dict = None,
            meta_data: dict = None,
            conversation_history: List[dict] = None,
    ) -> any:
        try:
            logger.info("=== ConversationService.generate_answer CALLED ===")
            logger.info("Generating answer calling ClaimsExplorerService ...")
            claims_service = ClaimsExplorerService(settings)
            # Run claims exploration
            result = await claims_service.explore_claims(
                question=user_input,
                jurisdictions=get_jurisdictions_from_request(additional_user_inputs)
            )

            logger.info("Claims exploration completed successfully")
            logger.info(f"Processed {len(result.agent_results)} agent configurations")

            # Print summary
            for agent_result in result.agent_results:
                logger.info(f"Config: {agent_result.config_name}, "
                            f"Success: {agent_result.success}, "
                            f"Time: {agent_result.execution_time:.2f}s")

            return result
        except BaseException as e:
            message = f"generate_answer:: {e}"
            logger.warn(message)
            return {
                "response": message
            }

    def get_intermediate_results_dict(self, results: any) -> dict:
        pass

    def get_system_output(self, results: any, answer_profile: AnswerProfile) -> any:
        pass

    def get_answer_text(self, results: any) -> str:
        pass

    def get_user_input(self, results: any) -> str:
        pass

    @staticmethod
    def on_conversation_task_done(user_id: str,
                                  conversation_id: str,
                                  conversation_entry_id: str,
                                  _: Future):
        conversation_entry = dynamo_db_v2.get_conversation_entry(user_id=user_id,
                                                                 conversation_id=conversation_id,
                                                                 conversation_entry_id=conversation_entry_id)
        if conversation_entry.status == RetrieveConversationEntryStatuses.CANCELLING.value:
            logger.info("Celery worker task has been revoked, cancelling asyncio task(s)")
            raise CancelledError(f"Cancellation request issued by user for "
                                 f"conversation {conversation_id} {conversation_entry_id}")
        logger.info(f"Additional search tasks for conversation {conversation_id} {conversation_entry_id} were finished")
