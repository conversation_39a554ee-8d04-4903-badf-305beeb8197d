from conversation_core.shared.services.profile_service import AnswerProfileService
from raslogger import LoggingFactory
from config import settings
from app.config.settings import Settings
from services.common_services import get_entitlement_client_v1

settings: Settings = settings.get_settings()
logger = LoggingFactory.get_logger(__name__)

# initiating the answer profile service with the required parameters
answer_profile_service: AnswerProfileService = AnswerProfileService(gcs_url=settings.GCS_URL,
                                                                    gcs_user_secret=settings.GCS_USER_SECRET,
                                                                    ras_config_base_url=settings.RAS_CONFIG_BASE_URL,
                                                                    entitlement_client=get_entitlement_client_v1(settings.GCS_URL))
