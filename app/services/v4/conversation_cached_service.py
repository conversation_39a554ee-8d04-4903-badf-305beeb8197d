import asyncio
import time
from typing import Optional, List

from conversation_core.shared.enums import ConversationActionType
from conversation_core.shared.models.answer_profile import AnswerProfile, ActionSequence
from conversation_core.shared.worker.worker_task import WorkerTask
from raslogger import LoggingFactory

from services.conversation_service import ConversationService

logger = LoggingFactory.get_logger(__name__)


class ConversationCachedService(ConversationService):
    async def generate_answer(
            self,
            user_id: str,
            user_input: str,
            conversation_id: str,
            conversation_entry_id: str,
            answer_solution_profile: str,
            answer_profile: AnswerProfile,
            jurisdictions: Optional[List[str]],
            content_types: Optional[List[str]],
            content_types_exclude: Optional[List[str]],
            conversation_action_type: ConversationActionType,
            auth_token: str,
            worker_task: WorkerTask,
            task_action_sequence: ActionSequence,
            additional_user_inputs: Optional[dict] = None,
            tags: Optional[List[str]] = None,
            user_session: dict = None,
            meta_data: dict = None,
            conversation_history: List[dict] = None,
    ) -> any:
        conversation_entry = self.dynamo_db_v2.get_cached_item_as_conversation_entry(user_input=user_input,
                                                                                     conversation_id=conversation_id,
                                                                                     conversation_entry_id=conversation_entry_id,
                                                                                     conversation_action_type=conversation_action_type,
                                                                                     answer_profile=answer_profile,
                                                                                     cache_criteria=jurisdictions)

        if not conversation_entry:
            raise RuntimeError(f"Cached item does not exist for Conversation {conversation_id}:{conversation_entry_id}. "
                               f"This may be due to the item being expired. This is a fatal exception")

        visible_after = conversation_entry.visible_after if conversation_entry else int(time.time())
        sleep_duration = int(visible_after - int(time.time()))
        if sleep_duration > 0:
            logger.info(f"Sleeping for {sleep_duration} seconds.")
            await asyncio.sleep(sleep_duration)

        # TODO: Implement from this point onward, specific to your worker
        # results: RagServiceOutput = RagServiceOutput(**conversation_entry.result.intermediate_results)
        # sso: SearchSummarizationOutput = SearchSummarizationOutput(**conversation_entry.result.system_output)

        # cached_answer = WestlawGenerateAnswer(rag_service_output=results, system_output=sso)

        # return cached_answer
