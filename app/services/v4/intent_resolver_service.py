from typing import Optional, List, Any

from conversation_core.shared.dynamo_helper_v2 import ConversationD<PERSON> as ConversationDBV2
from conversation_core.shared.enums import ConversationActionType
from conversation_core.shared.models.answer_profile import AnswerProfile, ActionSequence
from conversation_core.shared.services.v4.intent_resolver_base import IntentResolverServiceBaseV4
from conversation_core.shared.worker.worker_task import WorkerTask
from raslogger import LoggingFactory

from config.settings import get_settings
from services.answer_profile_service import answer_profile_service
from services.common_services import get_entitlement_client_v1

logger = LoggingFactory.get_logger(__name__)
settings = get_settings()
dynamo_db_v2 = ConversationDBV2(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)
entitlement_client = get_entitlement_client_v1(gcs_url=settings.GCS_URL)


class IntentResolverService(IntentResolverServiceBaseV4):
    def __init__(self):
        super().__init__(settings=settings,
                         dynamo_db_v2=dynamo_db_v2,
                         answer_profile_service=answer_profile_service,
                         entitlement_client=entitlement_client)

    async def check_intent(self,
                           user_id: str,
                           user_input: str,
                           conversation_id: str,
                           conversation_entry_id: str,
                           answer_solution_profile: str,
                           answer_profile: AnswerProfile,
                           jurisdictions: Optional[List[str]],
                           content_types: Optional[List[str]],
                           conversation_action_type: ConversationActionType,
                           auth_token: str,
                           worker_task: WorkerTask,
                           task_action_sequence: ActionSequence,
                           additional_user_inputs: Optional[dict] = None,
                           tags: Optional[List[str]] = None,
                           user_session: dict = None,
                           meta_data: dict = None) -> Any:
        pass

    def handle_error(
            self,
            user_id: str,
            conversation_id: str,
            conversation_entry_id: str,
            answer_profile: AnswerProfile,
            meta_data: dict,
            ex: Exception,
    ):
        pass

    def is_intent_category_sufficient_to_submit(self,
                                                intent_response: Any,
                                                profile: AnswerProfile,
                                                meta_data: dict):
        pass
