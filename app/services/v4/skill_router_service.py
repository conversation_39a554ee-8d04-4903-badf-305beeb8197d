from asyncio import CancelledError
from typing import Optional, List, Any

import requests
from requests import Response
from configuration_utils.constants import Constants as ConfigConstants
from conversation_core.shared.constants import Constants
from conversation_core.shared.dynamo_helper_v2 import ConversationDB as ConversationDBV2
from conversation_core.shared.enums import ConversationActionType, RetrieveConversationEntryStatuses, AalpSkill
from conversation_core.shared.models.answer_profile import AnswerProfile, ActionSequence
from conversation_core.shared.services.v4.skill_router_service_base import SkillRouterServiceBaseV4
from conversation_core.shared.worker.worker_task import WorkerTask
from raslogger import LoggingFactory

from config.settings import get_settings
from services.answer_profile_service import answer_profile_service
from services.common_services import get_entitlement_client_v2

logger = LoggingFactory.get_logger(__name__)
settings = get_settings()
dynamo_db_v2 = ConversationDBV2(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)
entitlement_client = get_entitlement_client_v2(gcs_url=settings.GCS_URL)


class SkillRouterService(SkillRouterServiceBaseV4):
    def __init__(self):
        super().__init__(settings=settings,
                         dynamo_db_v2=dynamo_db_v2,
                         answer_profile_service=answer_profile_service,
                         entitlement_client=entitlement_client)

    def do_skill_routing(self,
                         user_id: str,
                         user_input: str,
                         conversation_id: str,
                         conversation_entry_id: str,
                         answer_solution_profile: str,
                         overrides: Optional[dict],
                         filters: Optional[dict],
                         conversation_action_type: ConversationActionType,
                         auth_token: str,
                         worker_task: WorkerTask,
                         task_action_sequence: ActionSequence,
                         additional_user_inputs: Optional[dict] = None,
                         tags: Optional[List[str]] = None,
                         user_session: dict = None,
                         subscribed_skills: Optional[List[AalpSkill]] = None,
                         meta_data: dict = None) -> Any:
        try:
            if subscribed_skills is None:
                raise Exception("Subscribed skills are required for skill routing")
            response = self.call_request_evaluator(auth_token=auth_token,
                                                   user_session=user_session,
                                                   user_input=user_input,
                                                   answer_solution_profile=answer_solution_profile,
                                                   subscribed_skills=subscribed_skills,
                                                   task_action_sequence=task_action_sequence)
            return response.json()
        except CancelledError as cancel_error:
            dynamo_db_v2.update_conversation_entry(
                user_id=user_id,
                conversation_id=conversation_id,
                conversation_entry_id=conversation_entry_id,
                attribute_updates={Constants.CONV_STATUS: RetrieveConversationEntryStatuses.CANCELLED.value}
            )
            logger.info(f"Conversation Entry Status set to {RetrieveConversationEntryStatuses.CANCELLED.value}")
            raise cancel_error

    @staticmethod
    def call_request_evaluator(auth_token: str, user_session: dict, user_input: str, answer_solution_profile: str,
                               subscribed_skills: list[AalpSkill], task_action_sequence: ActionSequence) -> Response:
        validation_url = f"{settings.AI_CONVERSATIONS_URL}/api/v1/common/skill-router"
        headers = {
            "Authorization": f"Bearer {auth_token}",
            "Content-Type": "application/json",
            "x-tr-product-name": user_session.get(ConfigConstants.SESSION_PRODUCT_NAME, "unknown"),
            "x-tr-product-view": user_session.get(ConfigConstants.SESSION_PRODUCT_VIEW, "unknown"),
        }
        body = {
            "user_input": user_input,
            "answer_solution_profile": answer_solution_profile,
            "pipeline_name": task_action_sequence.pipeline,
            "subscribed_skills": [skill.value for skill in subscribed_skills],
        }
        response = requests.post(validation_url, json=body, headers=headers)
        return response

    def is_skill_redirection_suggested(self,
                                       skill_router_response: Any,
                                       profile: AnswerProfile,
                                       meta_data: dict):
        if "system_output" in skill_router_response:
            if "annotations" in skill_router_response["system_output"]:
                for annotation in skill_router_response["system_output"]["annotations"]:
                    if (annotation["type"] == "FeatureIntent"
                            and annotation["value"] != "YOUR_WORKER_SKILL"):  # FIXME: Implement this for your worker
                        return True
        return False

    def handle_error(
            self,
            user_id: str,
            conversation_id: str,
            conversation_entry_id: str,
            answer_profile: AnswerProfile,
            meta_data: dict,
            ex: Exception,
    ):
        # TODO: Implement this
        pass
