import asyncio
import time
from typing import Optional, List, Any

from conversation_core.shared.enums import ConversationActionType
from conversation_core.shared.models.answer_profile import AnswerProfile, ActionSequence
from conversation_core.shared.worker.worker_task import WorkerTask
from raslogger import LoggingFactory

from services.v4.intent_resolver_service import IntentResolverService

logger = LoggingFactory.get_logger(__name__)


class IntentResolverCachedService(IntentResolverService):
    async def check_intent(self,
                           user_id: str,
                           user_input: str,
                           conversation_id: str,
                           conversation_entry_id: str,
                           answer_solution_profile: str,
                           answer_profile: AnswerProfile,
                           jurisdictions: Optional[List[str]],
                           content_types: Optional[List[str]],
                           conversation_action_type: ConversationActionType,
                           auth_token: str,
                           worker_task: WorkerTask,
                           task_action_sequence: ActionSequence,
                           additional_user_inputs: Optional[dict] = None,
                           tags: Optional[List[str]] = None,
                           user_session: dict = None,
                           meta_data: dict = None,
                           conversation_history: List[dict] = None) -> Any:
        conversation_entry = self.dynamo_db_v2.get_cached_item_as_conversation_entry(user_input=user_input,
                                                                                     conversation_id=conversation_id,
                                                                                     conversation_entry_id=conversation_entry_id,
                                                                                     conversation_action_type=conversation_action_type,
                                                                                     answer_profile=answer_profile,
                                                                                     cache_criteria=jurisdictions)

        visible_after = conversation_entry.visible_after if conversation_entry else int(time.time())
        sleep_duration = int(visible_after - int(time.time()))

        if sleep_duration > 0:
            logger.info(f"Sleeping for {sleep_duration} seconds")
            await asyncio.sleep(sleep_duration)

        # TODO: Implement from this point onward, specific to your worker
        # intent_resolver_result = IntentResolverServiceOutput(**conversation_entry.result.system_output)
        # return intent_resolver_result
