from typing import Optional

import redis.exceptions
from aws_utils.redis_cache import RedisCache
from common_utils.cache_utils import CacheStrategy
from conversation_core.shared.dynamo_helper_v2 import ConversationDB as ConversationDBV2
from gcs_utils.entitlement_client import EntitlementClient
from gcs_utils.v2.entitlement_client import EntitlementClient as EntitlementClientV2
from raslogger import LoggingFactory
from redis.backoff import ConstantBackoff
from redis.retry import Retry

from config import settings
from config.settings import Settings

settings: Settings = settings.get_settings()
logger = LoggingFactory.get_logger(__name__)
redis_retry_strategy = Retry(ConstantBackoff(1), 5)
redis_retry_on_error = [redis.exceptions.ConnectionError, redis.exceptions.TimeoutError]
dynamo_db_v2 = ConversationDBV2(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)
_entitlement_client: Optional[EntitlementClientV2] = None
_entitlement_client_v1: Optional[EntitlementClient] = None


redis_cache_client = RedisCache(settings.REDIS_METRICS_CLUSTER,
                                redis_db=1,
                                retry=redis_retry_strategy,
                                retry_on_error=redis_retry_on_error,
                                retry_on_timeout=True,
                                socket_timeout=5,
                                socket_connect_timeout=5)


def get_entitlement_client_v1(gcs_url: str):
    global _entitlement_client_v1
    if _entitlement_client_v1 is None:
        _entitlement_client_v1 = EntitlementClient(gcs_entitlement_path=gcs_url,
                                                   redis_cache_client=redis_cache_client,
                                                   cache_strategy=CacheStrategy[settings.CACHE_STRATEGY.upper()])

    return _entitlement_client_v1


def get_entitlement_client_v2(gcs_url: str):
    global _entitlement_client
    if _entitlement_client is None:
        _entitlement_client = EntitlementClientV2(
            gcs_entitlement_path=settings.GCS_URL,
            secret_name=settings.GCS_USER_SECRET,
            secret_region=settings.REGION,
            redis_cache_client=redis_cache_client,
            cache_strategy=CacheStrategy[settings.CACHE_STRATEGY.upper()]
        )

    return _entitlement_client


# def get_llm_profile_service(settings):
#     global _llm_profile_service
#     if _llm_profile_service is None:
#         llm_env = "prod" if settings.ENVIRONMENT == "prod" else "preprod"
#         _llm_profile_service = LlmProfileService(ras_config_base_url=settings.RAS_CONFIG_BASE_URL,
#                                                  gcs_url=settings.GCS_URL,
#                                                  gcs_user_secret=settings.GCS_USER_SECRET,
#                                                  llm_environment=llm_env,
#                                                  entitlement_client=get_entitlement_client(settings.GCS_URL))
#     return _llm_profile_service
#
#
# def get_or_create_eventloop():
#     try:
#         loop = asyncio.get_running_loop()
#         logger.info(f"Using existing event loop with ID: {id(loop)}")
#         return loop
#     except RuntimeError as e:
#         logger.info(f"Creating new event loop: {e}")
#         loop = asyncio.new_event_loop()
#         asyncio.set_event_loop(loop)
#         logger.info(f"Created new event loop with ID: {id(loop)}")
#         return loop
#     except Exception as e:
#         logger.error(f"Error in get_or_create_eventloop: {e}")
#         return None
#
#
# def get_task_id():
#     return str(uuid.uuid4())
#
#
# def log_event_loop_id(task_name):
#     try:
#         loop = asyncio.get_running_loop()
#         logger.info(f"Task '{task_name}' running on event loop with ID: {id(loop)}")
#     except Exception as e:
#         logger.error(f"Error in log_event_loop_id {task_name}: {e}")
