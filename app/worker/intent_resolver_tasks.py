# WARNING: This is deprecated functionality using the V2 conversation endpoint and V3 task/service implementations!
# Use the V3 conversation endpoint and use V4 implementations instead.

# from typing import Optional, List
#
# from celery import shared_task
# from conversation_core.shared.dynamo_helper import ConversationDB
# from conversation_core.shared.dynamo_helper_v2 import ConversationDB as ConversationDBV2
# from conversation_core.shared.tasks.intent_resolver_tasks_base import IntentResolverTaskBase
# from conversation_core.shared.tasks.task_contracts import IntentResolverTasks
# from raslogger import LoggingFactory
#
# from config.settings import get_settings
# from services.intent_resolver_service import IntentResolverService
#
# logger = LoggingFactory.get_logger(__name__)
# settings = get_settings()
# intent_resolver_service = IntentResolverService()
# dynamo_db = ConversationDB(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)
# dynamo_db_v2 = ConversationDBV2(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)
#
#
# class IntentResolverTask(IntentResolverTasks):
#     @shared_task(bind=True, retry_backoff=True, retry_kwargs={"max_retries": 1}, name="evaluate_intent")
#     def evaluate_intent_task(
#         self,
#         is_new_conversation: bool,
#         user_id: str,
#         user_input: str,
#         answer_solution_profile: str,
#         jurisdictions_override: Optional[List[str]],
#         content_types_override: Optional[List[str]],
#         conversation_id: str,
#         conversation_entry_id: str,
#         conversation_action_type: str,
#         auth_token: str,
#         user_session: dict = None,
#         meta_data: Optional[dict] = None,
#     ):
#         return IntentResolverTaskImpl().evaluate_intent_task(
#             task=self,
#             is_new_conversation=is_new_conversation,
#             user_id=user_id,
#             user_input=user_input,
#             answer_solution_profile=answer_solution_profile,
#             jurisdictions_override=jurisdictions_override,
#             content_types_override=content_types_override,
#             conversation_id=conversation_id,
#             conversation_entry_id=conversation_entry_id,
#             conversation_action_type=conversation_action_type,
#             auth_token=auth_token,
#             user_session=user_session,
#             meta_data=meta_data,
#         )
#
#
# class IntentResolverTaskImpl(IntentResolverTaskBase):
#     def __init__(self):
#         super().__init__(
#             settings=settings,
#             dynamo_db=dynamo_db,
#             dynamo_db_v2=dynamo_db_v2,
#             intent_resolver_service=intent_resolver_service,
#             rag_service_wheel_name="TBD",
#         )
