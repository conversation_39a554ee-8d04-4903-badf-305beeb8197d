import logging
import os
from common_utils.logging_helper import init_logging
from ddtrace import patch_all
from raslogger import LoggingFactory
from config.celery_config import create_celery, reconfigure_ras_logger
from config.settings import get_settings

logger = LoggingFactory.get_logger(__name__)

settings = get_settings()
LOGGING_LEVEL = os.getenv('LOGGING_LEVEL')
if not LOGGING_LEVEL:
    os.environ['LOGGING_LEVEL'] = settings.LOGGING_LEVEL
else:
    settings.LOGGING_LEVEL = LOGGING_LEVEL

logger = LoggingFactory.get_logger(__name__)


if settings.DISABLE_ALL_PY_WARNINGS:
    LoggingFactory.get_logger("langchain.vectorstores").setLevel(logging.ERROR)
    LoggingFactory.get_logger("langchain.embeddings").setLevel(logging.ERROR)
    LoggingFactory.get_logger("py.warnings").setLevel(logging.ERROR)

patch_all(unittest=settings.patch_unittest)
init_logging(settings.LOGGER_FIX_LIST.split(","))

if not LOGGING_LEVEL:
    reconfigure_ras_logger(settings.LOGGING_LEVEL)

schedule_logger = logging.getLogger("apscheduler.executors.default")
schedule_logger.setLevel(logging.WARNING)

celery_app = create_celery()

if __name__ == "__main__":
    logger.info("Starting celery worker from main")
    celery_app.worker_main(
        argv=[
            "worker",
            "--loglevel=info",
            "--concurrency=1",
            "-P",
            "solo",
            "-E",
            "--without-mingle",
            "--without-gossip",
            "--heartbeat-interval",
            "30",
        ]
    )
