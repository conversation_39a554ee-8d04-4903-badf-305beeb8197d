ARG REGION_LONG
FROM 460300312212.dkr.ecr.${REGION_LONG}.amazonaws.com/tr-chainguard/python-fips:3.11-dev AS build

# Operations as root user
USER root
# python 3.9 is already installed on aws linux 2. The tr golden image doesn't remove it.
# We need to install openssl, tar and gzip to install helm
RUN apk add openssl && apk add gzip && apk add curl
RUN /usr/bin/python3.11 -m ensurepip --upgrade
RUN curl -O https://bootstrap.pypa.io/get-pip.py && python3 get-pip.py && rm get-pip.py
# Download and install helm
COPY /scripts /scripts
RUN chmod 700 /scripts/get_helm.sh
RUN /scripts/get_helm.sh
RUN rm -rf /scripts

RUN mkdir -p /home/<USER>/home/<USER>

# Operations as nonroot user
USER 65532:65532
ENV HOME="/home/<USER>"
WORKDIR /home/<USER>

COPY --chown=65532:65532 requirements.txt .

ARG PIP_EXTRA_INDEX_URL
ARG ARTIFACTORY_USER
ARG ARTIFACTORY_TOKEN

ENV PIP_EXTRA_INDEX_URL=https://${ARTIFACTORY_USER}:${ARTIFACTORY_TOKEN}@tr1.jfrog.io/tr1/api/pypi/pypi/simple

# Create venv and install dependencies
RUN python -m venv /home/<USER>/venv
ENV PATH="/home/<USER>/venv/bin:$PATH"
RUN pip install --no-cache-dir -r requirements.txt && pip cache purge

# Copy application code
COPY --chown=65532:65532 /app /home/<USER>/app

# Final stage
FROM 460300312212.dkr.ecr.us-east-1.amazonaws.com/tr-chainguard/python-fips:3.11

# Add Maintainer Info
LABEL maintainer="<EMAIL>"
# https://techtoc.thomsonreuters.com/non-functional/security/container-security/container-k8s-labels/#container-labels
LABEL com.tr.application-asset-insight-id="207891"
LABEL org.opencontainers.image.authors="<EMAIL>"
LABEL com.tr.service-contact="<EMAIL>"
LABEL org.opencontainers.image.source="https://github.com/tr/ras-search_ai-agent-claims-explorer/blob/main/Dockerfile"
# app version will be filled in by bakespec during the bake stage
LABEL org.opencontainers.image.version="APPVERSION"
LABEL org.opencontainers.image.vendor="Thomson Reuters"
LABEL org.opencontainers.image.url="https://github.com/tr/ras-search_ai-agent-claims-explorer"

# Switch to non-root user
USER 65532:65532
ENV HOME="/home/<USER>"
WORKDIR /home/<USER>

COPY --from=build --chown=65532:65532 /home/<USER>/home/<USER>
COPY --from=build --chown=65532:65532 /usr/local/bin/helm /usr/local/bin/helm

ENV PATH="/home/<USER>/venv/bin:/usr/local/bin:$PATH"
ENV PYTHONPATH="/home/<USER>/app:$PYTHONPATH"

# Set TMPDIR to the custom temp directory we created
ENV TMPDIR="/home/<USER>/tmp"

# Enable FIPS endpoints for all AWS services
ENV AWS_USE_FIPS_ENDPOINT=true

ENTRYPOINT ["celery","-A","main.celery_app","worker","--loglevel=info","--concurrency=1","--without-mingle","--without-gossip","--without-heartbeat"]