# Build stage
ARG REGION_LONG
FROM 460300312212.dkr.ecr.${REGION_LONG}.amazonaws.com/tr-chainguard/python-fips:3.11-dev AS build

# Switch to non-root user and set working directory
USER nonroot
WORKDIR /home/<USER>

# Copy only requirements first to leverage Docker cache
COPY --chown=nonroot:nonroot requirements.txt .

ARG PIP_EXTRA_INDEX_URL
ARG ARTIFACTORY_USER
ARG ARTIFACTORY_TOKEN

ENV PIP_EXTRA_INDEX_URL=https://${ARTIFACTORY_USER}:${ARTIFACTORY_TOKEN}@tr1.jfrog.io/tr1/api/pypi/pypi/simple

# Create venv and install dependencies
RUN python -m venv /home/<USER>/venv
ENV PATH="/home/<USER>/venv/bin:$PATH"
RUN pip install --no-cache-dir -r requirements.txt && pip cache purge

# Copy application code
COPY --chown=nonroot:nonroot /app /home/<USER>/app

# Download and install Helm using Python
RUN python -c "import urllib.request; urllib.request.urlretrieve('https://get.helm.sh/helm-v3.8.2-linux-amd64.tar.gz', 'helm.tar.gz')" && \
    tar -zxvf helm.tar.gz && \
    mv linux-amd64/helm /home/<USER>/helm && \
    rm -rf helm.tar.gz linux-amd64

# Final stage
FROM 460300312212.dkr.ecr.${REGION_LONG}.amazonaws.com/tr-chainguard/python-fips:3.11

# Add Maintainer Info and Labels
LABEL maintainer="<EMAIL>"
# https://techtoc.thomsonreuters.com/non-functional/security/container-security/container-k8s-labels/#container-labels
LABEL com.tr.application-asset-insight-id="207891"
LABEL org.opencontainers.image.authors="<EMAIL>"
LABEL com.tr.service-contact="<EMAIL>"
LABEL org.opencontainers.image.source="https://github.com/tr/ras-search_ai-agent-claims-explorer/blob/main/Dockerfile"
# app version will be filled in by bakespec during the bake stage
LABEL org.opencontainers.image.version="APPVERSION"
LABEL org.opencontainers.image.vendor="Thomson Reuters"
LABEL org.opencontainers.image.url="https://github.com/tr/ras-search_ai-agent-claims-explorer"

# Switch to non-root user
USER 65532
ENV HOME="/home/<USER>"
WORKDIR /home/<USER>

# Copy venv, app, and Helm from build stage
COPY --from=build --chown=65532:65532 /home/<USER>/venv /home/<USER>/venv
COPY --from=build --chown=65532:65532 /home/<USER>/app /home/<USER>/app
COPY --from=build --chown=65532:65532 /home/<USER>/helm /usr/local/bin/helm

# Set environment variables
ENV PATH="/home/<USER>/venv/bin:/usr/local/bin:$PATH"
ENV PYTHONPATH="/home/<USER>/app:$PYTHONPATH"

ENV TMPDIR="/home/<USER>/tmp"

# Enable FIPS endpoints for all AWS services
ENV AWS_USE_FIPS_ENDPOINT=true

EXPOSE 8000

ENTRYPOINT ["celery","-A","main.celery_app","worker","--loglevel=info","--without-mingle","--without-gossip","--without-heartbeat"]