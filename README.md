<!-- START badge-template.html --><svg fill="none" viewBox="0 0 120 120" width="120" height="120" xmlns="http://www.w3.org/2000/svg">
  <foreignObject width="100%" height="100%">
    <div xmlns="http://www.w3.org/1999/xhtml">
      <a href="https://github.com/tr/ras-search_ai-agent-claims-explorer/blob/tr-cicd-resources/tr/ras-search_ai-agent-claims-explorer/badges/init_checkin/last-badge-update.svg" target="_blank">
        <img alt="Last Updated" src="https://github.com/tr/ras-search_ai-agent-claims-explorer/blob/tr-cicd-resources/badges/init_checkin/last-badge-update.svg">
      </a>
      <br />
      <a href="https://github.com/tr/ras-search_ai-agent-claims-explorer/actions/runs/17752853678" target="_blank">
        <img alt="CI Build" src="https://github.com/tr/ras-search_ai-agent-claims-explorer/actions/workflows/python-build.yml/badge.svg?branch=init_checkin">
      </a>
      <br />
      <a href="https://github.com/tr/ras-search_ai-agent-claims-explorer/pulls?q=is:pr+created:%3C%3D2025-09-09+is%3Aopen" target="_blank">
      <img alt="Stale Pull Requests" src="https://github.com/tr/ras-search_ai-agent-claims-explorer/blob/tr-cicd-resources/badges/init_checkin/stale-pr-count.svg">
      </a>
      <br />
      <a href="https://github.com/tr/ras-search_ai-agent-claims-explorer/labels/dependencies?q=+is%3Aopen" target="_blank">
      <img alt="Dated Dependencies" src="https://github.com/tr/ras-search_ai-agent-claims-explorer/blob/tr-cicd-resources/badges/init_checkin/dated-dependency-count.svg">
      </a>
      <br />
      <a href="https://github.com/tr/ras-search_ai-agent-claims-explorer/runs/50450800488" target="_blank">
        <img alt="Code Coverage" src="https://github.com/tr/ras-search_ai-agent-claims-explorer/blob/tr-cicd-resources/badges/init_checkin/code-coverage.svg">
      </a>
      <br />
      <a href="https://github.com/tr/ras-search_ai-agent-claims-explorer/runs/50450847443" target="_blank">
        <img alt="Lines of Code" src="https://github.com/tr/ras-search_ai-agent-claims-explorer/blob/tr-cicd-resources/badges/init_checkin/lines-of-code.svg">
      </a>
      <br />
      <a href="https://github.com/tr/ras-search_ai-agent-claims-explorer/releases/latest" target="_blank">
      <img alt="Latest Release" src="https://github.com/tr/ras-search_ai-agent-claims-explorer/blob/tr-cicd-resources/badges/main/latest-release.svg">
      </a>
    </div>
  </foreignObject>
</svg>

<!-- END badge-template.html -->
# RAS Search AI RAG Template

## Running locally

1. Set the following env vars in the run config.

    ```
    PYTHONUNBUFFERED=1;
    DD_ENV=local;
    DD_PROFILING_ENABLED=false;
    DD_SERVICE=ai-rag-template;
    DD_TRACE_ENABLED=True;
    DD_VERSION=0.0.1;
    RESOURCES_DIR=./app/config/resources;
    HOSTNAME=[put your workstation name here]
    ```
2. Setup dependencies containers

   The worker requires the following dependencies to run:
    - Redis
    - Datadog agent
    - DynamoDB

    1. Pull the needed Docker images to run the worker

        ```
        docker pull redis:latest
        docker pull amazon/dynamodb-local
        docker pull datadog/agent:latest
        ``` 

    2. Run Docker containers based on the pulled images

       ```
       docker run -d -p 6379:6379 --name redis redis
       docker run -d -p 8000:8000 --name dynamodb amazon/dynamodb-local
       docker run -d -p 8125:8125 -p 8126:8126  -e DD_HOSTNAME=local.developer -e DD_ENV=local -e DD_API_KEY=[get this from secret manager] --name datadog_agent datadog/agent:latest
       ```
3. Cloud-tool into the dedicated AWS account.
   ```
   cloud-tool login -u 'mgmt\your_user_id' -p your_password

