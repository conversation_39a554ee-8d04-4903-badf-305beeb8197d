[project]
name = "ras-search-ai-agent-claims-explorer"
version = "0.1.0"
description = "AI Agent Westlaw Claims Explorer"
authors = []
readme = "README.md"

[tool.poetry]
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "~3.11"
raslogger = "^0.4.4"
ras-shared-python-aws-utils="^1.18.14"
ras-shared-python-common-utils="^1.18.14"
ras-shared-python-conversation-core="^1.18.14"
ras-shared-python-gcs-utils="^1.18.14"
ras-shared-python-jurisdiction-utils="^1.18.14"
ras-search-claims-explorer-service="^0.1.0"
celery = "^5.3.1"
apscheduler = "^3.10.4"
dataclasses-json = "^0.5.14"
regex = "2024.9.11"
jinja2 = "^3.1.6"

[tool.poetry.group.dev.dependencies]
poethepoet = "^0.24.4"
mock = "^5.1.0"
mockito = "^1.4.0"
pytest = "8.2"
pytest-env = "1.0.0"
pytest-cov = ">=4.1.0"
pytest-html = "^4.1.1"
pytest-asyncio = "^0.24.0"
black = "23.3.0"
flake8 = "^6.0.0"
flake8-bugbear = "^23.2.13"

[tool.black]
# See other configurations available at https://pypi.org/project/black/
line-length = 120
target-version = ['py311']

[tool.poe.tasks]
pytest = "pytest --cache-clear --junitxml=reports/pytest_junit.xml --html=reports/pytest.html --self-contained-html --cov-report=html:reports/coverage_html --cov-report=xml:reports/coverage.xml"

[[tool.poetry.source]]
name = "tr"
url = "https://tr1.jfrog.io/artifactory/api/pypi/pypi/simple"
priority = "primary"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"