name: TrOrg-GitHubReleases-Publish

on:
  # COMMENT for manually releasing versions
  workflow_run:
    workflows:
      - TrOrg-Java-CiBuild
      - TrOrg-JavaScript-CiBuild
      - TrOrg-Python-CiBuild
      - TrOrg-CSharp-CiBuild
      - TrOrg-CloudIaC-CiTemplateValidation
    types:
      - completed
    branches:
      - main
  # UNCOMMENT for manually releasing versions
  # workflow_dispatch:

env:
  # Define git user email and name to be used for git commits
  GIT_USER_EMAIL: "<EMAIL>"
  GIT_USER_NAME: "GitHub Actions"
  ENV_CONFIG_FILE: ".github/env-variables.txt"

jobs:
  release:
    runs-on: ubuntu-latest
    # COMMENT condition for manually releasing versions
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    steps:
      - uses: release-drafter/release-drafter@3f0f87098bd6b5c5b9a36d49c41d998ea58f9348 # v6.0.0
        with:
          publish: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Checkout Git Repo
        uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7

      - name: Set environment variables defined in the ENV_CONFIG_FILE
        shell: bash
        run: |
          IFS="["
          while read -r -a LINE || [ -n "$LINE" ] ; do
            if [[ -n "$LINE" && ${LINE::1} != "#" ]]; then
              echo "$LINE" | xargs;
            fi
          done < ${{ env.ENV_CONFIG_FILE }} >> $GITHUB_ENV

      - name: Get All Releases
        id: get-releases
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        with:
          script: |
            async function getAllReleases() {
              let releases = await github.paginate(github.rest.repos.listReleases,{
                owner: context.repo.owner,
                repo: context.repo.repo,
                per_page: 100,
              },
              (response) => response.data.map((release) => ({
                id: release.id,
                tag_name: release.tag_name,
                created_at: release.created_at,
                name: release.name,
                target_commitish: release.target_commitish,
                draft: release.draft,
                prerelease: release.prerelease,
                published_at: release.published_at,
              })));

              return releases;
            }

            return getAllReleases();
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Delete Older Releases
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        with:
          script: |
            async function deleteOlderReleases(releases, releasesToKeep) {
              if (releases.length <= releasesToKeep) {
                console.log("No older releases to delete.");
                return;
              }

              releases.sort((a, b) => new Date(b.published_at) - new Date(a.published_at));

              const releasesToDelete = releases.slice(releasesToKeep);

              for (const release of releasesToDelete) {
                await github.rest.repos.deleteRelease({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  release_id: release.id
                });
                console.log(`Deleted release ${release.tag_name}`);
              }
            }

            const releases = ${{ steps.get-releases.outputs.result }};
            const releasesToKeep = parseInt(process.env.RELEASES_TO_KEEP || 20);

            return deleteOlderReleases(releases, releasesToKeep);
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # Repo badges job to inform repo health
  badges:
    if: ${{ github.event.workflow_run.name != 'TrOrg-CloudIaC-CiTemplateValidation' }}
    runs-on: ubuntu-latest
    needs: release
    env:
      # Badge variables
      BADGE_ROOT_DIR: badges
      BADGE_BRANCH_NAME: tr-cicd-resources
      BADGE_BRANCH_DIR: ${{ github.ref_name}}
      LATEST_RELEASE_BADGE_FILE_NAME: latest-release.svg

    steps:
      # Setup/Checkout repo
      - name: Checkout Git Repo
        uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7
        with:
          fetch-depth: 0

      - name: Set environment variables defined in the ENV_CONFIG_FILE
        shell: bash
        run: |
          IFS="["
          while read -r -a LINE || [ -n "$LINE" ] ; do
            if [[ -n "$LINE" && ${LINE::1} != "#" ]]; then
              echo "$LINE" | xargs;
            fi
          done < ${{ env.ENV_CONFIG_FILE }} >> $GITHUB_ENV

      - name: Checkout Git Repo
        uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7
        with:
          fetch-depth: 0
          ref: ${{ env.BADGE_BRANCH_NAME }}

      # BADGE: Latest Release: vX.Y.Z
      # Latest GH Releases version
      - name: Get latest release number
        id: release
        run: |
          # Generates a GitHub Workflow output named `releaseName`
          export RELEASE_NAME=`curl -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" -H "User-Agent *" -H "Accept: application/vnd.github.v3+json" -s  $GITHUB_API_URL/repos/${{ github.repository }}/releases/latest | jq -r .name`
          echo "Latest Release Version = ${RELEASE_NAME}"
          echo "releaseName=${RELEASE_NAME}" >> $GITHUB_OUTPUT

      - name: Generate latest release badge image
        if: ${{ env.BRANCH_PROTECTION != 'true' }}
        uses: emibcn/badge-action@808173dd03e2f30c980d03ee49e181626088eee8 # v2.0.3
        with:
          label: 'Latest Release'
          status: ${{ steps.release.outputs.releaseName }}
          color: 'green'
          path: ${{ env.BADGE_ROOT_DIR}}/${{ env.BADGE_BRANCH_DIR }}/${{ env.LATEST_RELEASE_BADGE_FILE_NAME }}

      #  Commit all newly generated badge images to tr-cicd-resources branch
      - name: Commit badge images
        if: ${{ env.BRANCH_PROTECTION != 'true' }}
        uses: tr/p7e_github-actions-badge-creation@87d690510c9a77859ff2137ca6ee56381038d0d7 # v1.0.2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}