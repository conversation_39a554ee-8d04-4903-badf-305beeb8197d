apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ .Values.application.name }}-secret-reader-role-{{ .Values.environment }}
  labels:
{{ include "mychart.common_labels" .  | indent 4 }}
  annotations:
    app.tr.com/deployment-specification: "https://github.com/tr/ras-search_ai-agent-claims-explorer/blob/main/helm2/infrastructure/templates/role.yaml"
{{ include "mychart.resource_annotations" . | indent 4 }}
rules:
  - apiGroups: [""]
    resources: ["secrets", "services", "endpoints"]
    verbs: ["post", "put", "get", "list", "watch"]  # Specify the required verbs (permissions)
