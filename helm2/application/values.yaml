deployment :
  replicaCount:
    dev: 1
    ci: 1
    int: 3
    qa: 25
    prod: 25
  terminationDrainDuration: 600s
  terminationGracePeriodSeconds: 600
  image:
    containerPort : 8000
    pullPolicy: IfNotPresent
    resources:
      limits:
        cpu: 2
        memory: 10Gi
      requests:
        cpu: 2
        memory: 8Gi
    livenessProbe:
      httpGet:
        port: 8000
      initialDelaySeconds: 40
      timeoutSeconds: 10
      periodSeconds: 30
      failureThreshold: 5
    startupProbe:
      periodSeconds: 10
      failureThreshold: 10
    readinessProbe:
      httpGet:
        port: 8000
      initialDelaySeconds: 40
      periodSeconds: 20
hpa:
  max:
    dev: 1
    ci: 1
    int: 3
    qa: 25
    prod: 25
  cpu:
    targetAverageUtilization: 50
  memory:
    targetAverageUtilization: 65
poddb:
  maxUnavailable: "50%"

virtualservice:
  port:
    number: 80
  weight: 100

destination:
  subset: app-deployment