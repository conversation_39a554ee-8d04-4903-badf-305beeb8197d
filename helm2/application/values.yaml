deployment :
  replicaCount:
    dev: 2
    ci: 2
    int: 2
    qa: 25
    prod: 40
  terminationDrainDuration: 600s
  terminationGracePeriodSeconds: 600
  image:
    containerPort : 8000
    pullPolicy: IfNotPresent
    resources:
      limits:
        cpu: 2
        memory: 6Gi
      requests:
        cpu: 2
        memory: 3Gi
    livenessProbe:
      httpGet:
        port: 8000
      initialDelaySeconds: 40
      timeoutSeconds: 10
      periodSeconds: 30
      failureThreshold: 5
    startupProbe:
      periodSeconds: 10
      failureThreshold: 10
    readinessProbe:
      httpGet:
        port: 8000
      initialDelaySeconds: 40
      periodSeconds: 20
hpa:
  max:
    dev: 2
    ci: 2
    int: 2
    qa: 25
    prod: 40
  cpu:
    targetAverageUtilization: 50
  memory:
    targetAverageUtilization: 65
poddb:
  maxUnavailable: "50%"

virtualservice:
  port:
    number: 80
  weight: 100

destination:
  subset: app-deployment